@echo off
chcp 65001 >nul
echo ========================================
echo 快手采集工具 - 依赖安装脚本
echo 适配 Python 3.13
echo ========================================
echo.

REM 设置环境变量防止中文乱码
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
set PYTHONLEGACYWINDOWSSTDIO=utf-8

REM 检查Python版本
echo [1/4] 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo ✗ 错误：未找到Python，请先安装Python 3.7+
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查Python版本是否符合要求
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo 当前Python版本: %PYTHON_VERSION%

REM 升级pip
echo [2/4] 升级pip...
python -m pip install --upgrade pip
if %errorlevel% neq 0 (
    echo ✗ pip升级失败，但继续安装依赖...
) else (
    echo ✓ pip升级成功
)

REM 安装依赖包
echo [3/4] 安装依赖包...
echo 正在安装，这可能需要几分钟时间...

REM 首先尝试安装PyQt5
echo 安装PyQt5...
pip install PyQt5>=5.15.0 PyQtWebEngine>=5.15.0
if %errorlevel% equ 0 (
    echo ✓ PyQt5安装成功
    set QT_INSTALLED=1
) else (
    echo ✗ PyQt5安装失败，尝试安装PyQt6...
    pip install PyQt6>=6.0.0 PyQt6-WebEngine>=6.0.0
    if %errorlevel% equ 0 (
        echo ✓ PyQt6安装成功
        set QT_INSTALLED=1
    ) else (
        echo ✗ PyQt5和PyQt6都安装失败
        set QT_INSTALLED=0
    )
)

REM 安装其他依赖
echo 安装其他依赖包...
pip install requests>=2.25.0 urllib3>=1.26.0 pandas>=1.3.0 openpyxl>=3.0.0 pyinstaller>=5.0.0

if %errorlevel% equ 0 (
    echo ✓ 其他依赖包安装成功
) else (
    echo ✗ 部分依赖包安装失败
)

REM 验证安装
echo [4/4] 验证安装...
python -c "import sys; print('Python版本:', sys.version)"

if %QT_INSTALLED% equ 1 (
    python -c "try: import PyQt5; print('✓ PyQt5可用'); except: pass"
    python -c "try: import PyQt6; print('✓ PyQt6可用'); except: pass"
) else (
    echo ✗ 警告：PyQt未正确安装，程序可能无法运行
)

python -c "try: import requests, pandas, openpyxl; print('✓ 核心依赖包可用'); except Exception as e: print('✗ 核心依赖包缺失:', e)"
python -c "try: import PyInstaller; print('✓ PyInstaller可用'); except Exception as e: print('✗ PyInstaller缺失:', e)"

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo 现在可以运行以下命令：
echo 1. 运行程序：python main.py
echo 2. 打包程序：build.bat
echo.
echo 如果遇到问题，请检查：
echo - Python版本是否为3.7+
echo - 网络连接是否正常
echo - 是否有杀毒软件阻止安装
echo.
pause
