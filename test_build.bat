@echo off
chcp 65001 >nul
echo 测试打包环境...

REM 设置环境变量
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

echo 检查Python版本:
python --version

echo.
echo 检查PyQt:
python -c "try: import PyQt5; print('✓ PyQt5可用'); except: print('✗ PyQt5不可用')"
python -c "try: import PyQt6; print('✓ PyQt6可用'); except: print('✗ PyQt6不可用')"

echo.
echo 检查其他依赖:
python -c "try: import requests; print('✓ requests可用'); except: print('✗ requests不可用')"
python -c "try: import pandas; print('✓ pandas可用'); except: print('✗ pandas不可用')"
python -c "try: import openpyxl; print('✓ openpyxl可用'); except: print('✗ openpyxl不可用')"
python -c "try: import PyInstaller; print('✓ PyInstaller可用'); except: print('✗ PyInstaller不可用')"

echo.
echo 检查主程序文件:
if exist "main.py" (
    echo ✓ main.py存在
) else (
    echo ✗ main.py不存在
)

if exist "快手采集工具.spec" (
    echo ✓ 快手采集工具.spec存在
) else (
    echo ✗ 快手采集工具.spec不存在
)

echo.
echo 测试完成！
pause
