# 快手采集工具 - 打包说明

本文档说明如何将快手采集工具打包成EXE可执行文件，特别针对Python 3.13进行了优化，并解决了中文乱码问题。

## 📋 系统要求

- **操作系统**: Windows 10/11
- **Python版本**: 3.7+ (推荐 3.13)
- **内存**: 至少 4GB RAM
- **磁盘空间**: 至少 2GB 可用空间

## 🚀 快速开始

### 方法一：使用批处理脚本（推荐）

1. **安装依赖**（首次使用）：
   ```cmd
   install_dependencies.bat
   ```

2. **打包程序**：
   ```cmd
   build.bat
   ```

### 方法二：使用Python脚本

1. **安装依赖**：
   ```cmd
   pip install -r requirements.txt
   ```

2. **打包程序**：
   ```cmd
   python build.py
   ```

### 方法三：手动安装和打包

1. **安装依赖**：
   ```cmd
   pip install PyQt5 PyQtWebEngine requests pandas openpyxl pyinstaller
   ```

2. **打包程序**：
   ```cmd
   python -m PyInstaller --clean --noconfirm "快手采集工具.spec"
   ```

## 📁 文件说明

### 核心文件
- `main.py` - 主程序文件
- `data_collector.py` - 数据采集模块
- `product_query.py` - 商品查询模块
- `category_parser.py` - 类目解析模块
- `cookie_exporter.py` - Cookie管理模块

### 打包相关文件
- `build.bat` - Windows批处理打包脚本（推荐）
- `build.py` - Python打包脚本
- `快手采集工具.spec` - PyInstaller配置文件
- `requirements.txt` - 依赖包列表
- `install_dependencies.bat` - 依赖安装脚本

### 辅助文件
- `run.py` - 程序启动脚本
- `打包说明.md` - 本说明文档

## 🔧 打包配置特性

### Python 3.13 优化
- ✅ 兼容Python 3.13新特性
- ✅ 优化模块导入和依赖检测
- ✅ 改进的内存管理配置

### 中文乱码解决方案
- ✅ UTF-8编码环境设置
- ✅ Windows清单文件配置
- ✅ 控制台代码页设置
- ✅ 环境变量优化

### 打包优化
- ✅ 自动依赖检测
- ✅ 文件大小优化
- ✅ 启动速度优化
- ✅ 错误处理改进

## 📊 打包结果

成功打包后，您将得到：
- `dist/快手采集工具.exe` - 主可执行文件
- 文件大小约：50-100MB
- 启动时间：3-10秒（首次启动较慢）

## ⚠️ 常见问题

### 1. 打包失败
**问题**: PyInstaller执行失败
**解决方案**:
```cmd
pip uninstall pyinstaller
pip install pyinstaller
```

### 2. 依赖包缺失
**问题**: 提示缺少某些模块
**解决方案**:
```cmd
pip install -r requirements.txt
```

### 3. 中文显示乱码
**问题**: 程序界面中文显示异常
**解决方案**: 
- 确保使用提供的打包脚本
- 检查系统区域设置是否为中文

### 4. 杀毒软件误报
**问题**: 杀毒软件阻止运行
**解决方案**:
- 将程序添加到杀毒软件白名单
- 临时关闭实时保护进行打包

### 5. 内存不足
**问题**: 打包过程中内存不足
**解决方案**:
- 关闭其他程序释放内存
- 使用64位Python版本

## 🔍 调试模式

如需调试打包后的程序，可以修改`快手采集工具.spec`文件：
```python
console=True,  # 改为True显示控制台
debug=True,    # 改为True启用调试模式
```

## 📝 版本信息

- **打包脚本版本**: 2.0
- **支持Python版本**: 3.7 - 3.13
- **PyInstaller版本**: 5.0+
- **最后更新**: 2024-08-03

## 🆘 技术支持

如果遇到问题，请检查：
1. Python版本是否符合要求
2. 所有依赖包是否正确安装
3. 系统是否有足够的磁盘空间
4. 杀毒软件是否阻止了打包过程

## 📄 许可证

本打包脚本遵循与主程序相同的许可证。
