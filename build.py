#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手采集工具 - Python打包脚本
适配 Python 3.13 + 防中文乱码优化
"""

import os
import sys
import shutil
import subprocess
import platform
from pathlib import Path


def print_header():
    """打印脚本头部信息"""
    print("=" * 50)
    print("快手采集工具 - Python打包脚本 v2.0")
    print("适配 Python 3.13 + 防中文乱码优化")
    print("=" * 50)
    print()


def check_python_version():
    """检查Python版本"""
    print("[1/7] 检查Python环境...")
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("✗ 错误：需要Python 3.7或更高版本")
        return False
    
    print("✓ Python版本符合要求")
    return True


def check_dependencies():
    """检查依赖包"""
    print("[2/7] 检查依赖包...")
    
    required_packages = [
        ('PyInstaller', 'pyinstaller'),
        ('requests', 'requests'),
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl')
    ]
    
    missing_packages = []
    
    # 检查PyQt
    pyqt_available = False
    try:
        import PyQt5
        print("✓ 找到PyQt5")
        pyqt_available = True
    except ImportError:
        try:
            import PyQt6
            print("✓ 找到PyQt6")
            pyqt_available = True
        except ImportError:
            print("✗ 未找到PyQt5或PyQt6")
            missing_packages.append('PyQt5 或 PyQt6')
    
    # 检查其他包
    for display_name, package_name in required_packages:
        try:
            __import__(package_name)
            print(f"✓ 找到{display_name}")
        except ImportError:
            print(f"✗ 未找到{display_name}")
            missing_packages.append(display_name)
    
    if missing_packages:
        print(f"✗ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✓ 所有依赖包检查完成")
    return True


def clean_build_files():
    """清理构建文件"""
    print("[3/7] 清理旧文件...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.pyc']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"删除目录: {dir_name}")
            shutil.rmtree(dir_name, ignore_errors=True)
    
    # 清理.pyc文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                try:
                    os.remove(os.path.join(root, file))
                except:
                    pass
    
    print("✓ 清理完成")


def setup_environment():
    """设置环境变量"""
    print("[4/7] 设置环境变量...")
    
    env_vars = {
        'PYTHONIOENCODING': 'utf-8',
        'PYTHONUTF8': '1',
        'PYTHONLEGACYWINDOWSSTDIO': 'utf-8'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"设置 {key}={value}")
    
    print("✓ 环境变量设置完成")


def check_spec_file():
    """检查spec文件"""
    print("[5/7] 检查配置文件...")
    
    spec_file = "快手采集工具.spec"
    if not os.path.exists(spec_file):
        print(f"✗ 错误：未找到配置文件 {spec_file}")
        return False
    
    print(f"✓ 找到配置文件: {spec_file}")
    return True


def run_pyinstaller():
    """运行PyInstaller"""
    print("[6/7] 开始打包...")
    print("这可能需要几分钟时间，请耐心等待...")
    print()
    
    spec_file = "快手采集工具.spec"
    cmd = [sys.executable, '-m', 'PyInstaller', '--clean', '--noconfirm', spec_file]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ PyInstaller执行成功")
            return True
        else:
            print("✗ PyInstaller执行失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 执行PyInstaller时出错: {e}")
        return False


def check_result():
    """检查打包结果"""
    print("[7/7] 检查打包结果...")
    
    exe_path = Path("dist") / "快手采集工具.exe"
    
    if exe_path.exists():
        file_size = exe_path.stat().st_size
        print("✓ 打包成功！")
        print(f"可执行文件: {exe_path}")
        print(f"文件大小: {file_size:,} 字节 ({file_size/1024/1024:.1f} MB)")
        return True
    else:
        print("✗ 打包失败：未找到生成的exe文件")
        return False


def print_success_info():
    """打印成功信息"""
    print()
    print("=" * 50)
    print("✓ 打包成功！")
    print("=" * 50)
    print("可执行文件位置: dist\\快手采集工具.exe")
    print()
    print("使用提示：")
    print("- 首次运行可能需要较长时间")
    print("- 请确保data目录和相关文件与exe在同一目录")
    print("- 如遇到问题，请检查Windows Defender等杀毒软件")
    print()


def print_error_info():
    """打印错误信息"""
    print()
    print("=" * 50)
    print("✗ 打包失败！")
    print("=" * 50)
    print("常见解决方案：")
    print("1. 确保所有依赖包已正确安装")
    print("2. 检查Python版本是否为3.7+")
    print("3. 尝试重新安装PyInstaller")
    print("4. 检查是否有杀毒软件阻止打包过程")
    print("5. 确保有足够的磁盘空间")
    print()


def main():
    """主函数"""
    print_header()
    
    # 检查步骤
    steps = [
        check_python_version,
        check_dependencies,
        clean_build_files,
        setup_environment,
        check_spec_file,
        run_pyinstaller,
        check_result
    ]
    
    for step in steps:
        if not step():
            print_error_info()
            return False
    
    print_success_info()
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n用户中断打包过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n打包过程中出现异常: {e}")
        sys.exit(1)
