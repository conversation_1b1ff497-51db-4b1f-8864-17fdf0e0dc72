@echo off
chcp 65001 >nul
echo ========================================
echo 快手采集工具 - 自动打包脚本 v2.0
echo 适配 Python 3.13 + 防中文乱码优化
echo ========================================
echo.

REM 设置环境变量防止中文乱码
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
set PYTHONLEGACYWINDOWSSTDIO=utf-8

REM 检查Python版本
echo [1/6] 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo ✗ 错误：未找到Python，请确保Python已正确安装并添加到PATH
    pause
    exit /b 1
)

REM 检查PyInstaller是否安装
echo [2/6] 检查PyInstaller...
python -c "import PyInstaller" 2>nul
if %errorlevel% neq 0 (
    echo ✗ PyInstaller未安装，正在自动安装...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo ✗ PyInstaller安装失败，请手动安装: pip install pyinstaller
        pause
        exit /b 1
    )
    echo ✓ PyInstaller安装成功
) else (
    echo ✓ PyInstaller已安装
)

REM 检查必要的依赖包
echo [3/6] 检查依赖包...
python -c "import PyQt5" 2>nul
if %errorlevel% neq 0 (
    python -c "import PyQt6" 2>nul
    if %errorlevel% neq 0 (
        echo ✗ 错误：未找到PyQt5或PyQt6，请先安装: pip install PyQt5 或 pip install PyQt6
        pause
        exit /b 1
    ) else (
        echo ✓ 找到PyQt6
    )
) else (
    echo ✓ 找到PyQt5
)

python -c "import requests, pandas, openpyxl" 2>nul
if %errorlevel% neq 0 (
    echo ✗ 错误：缺少必要依赖包，请安装: pip install requests pandas openpyxl
    pause
    exit /b 1
)
echo ✓ 依赖包检查完成

REM 清理之前的构建文件
echo [4/6] 清理旧文件...
if exist "build" (
    echo 删除 build 目录...
    rmdir /s /q "build"
)
if exist "dist" (
    echo 删除 dist 目录...
    rmdir /s /q "dist"
)
if exist "__pycache__" (
    echo 删除 __pycache__ 目录...
    rmdir /s /q "__pycache__"
)
for /r %%i in (*.pyc) do del "%%i" 2>nul
echo ✓ 清理完成

REM 开始打包
echo [5/6] 开始打包...
echo 使用配置文件: 快手采集工具.spec
echo 这可能需要几分钟时间，请耐心等待...
echo.

python -m PyInstaller --clean --noconfirm "快手采集工具.spec"

REM 检查打包结果
echo [6/6] 检查打包结果...
if %errorlevel% equ 0 (
    if exist "dist\快手采集工具.exe" (
        echo.
        echo ========================================
        echo ✓ 打包成功！
        echo ========================================
        echo 可执行文件位置: dist\快手采集工具.exe
        echo 文件大小:
        for %%A in ("dist\快手采集工具.exe") do echo   %%~zA 字节
        echo.
        echo 提示：
        echo - 首次运行可能需要较长时间
        echo - 请确保data目录和相关文件与exe在同一目录
        echo - 如遇到问题，请检查Windows Defender等杀毒软件
        echo.
    ) else (
        echo ✗ 打包失败：未找到生成的exe文件
        echo 请检查上方的错误信息
        goto :error
    )
) else (
    echo ✗ 打包过程中出现错误
    goto :error
)

echo 按任意键退出...
pause >nul
exit /b 0

:error
echo.
echo ========================================
echo ✗ 打包失败！
echo ========================================
echo 常见解决方案：
echo 1. 确保所有依赖包已正确安装
echo 2. 检查Python版本是否为3.7+
echo 3. 尝试重新安装PyInstaller: pip uninstall pyinstaller ^&^& pip install pyinstaller
echo 4. 检查是否有杀毒软件阻止打包过程
echo 5. 确保有足够的磁盘空间
echo.
echo 按任意键退出...
pause >nul
exit /b 1
