# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from pathlib import Path
import glob

# 项目根目录
project_root = Path.cwd()

# 数据文件列表 - 更精确的文件包含
datas = []

# 添加data目录（如果存在）
if (project_root / "data").exists():
    datas.append((str(project_root / "data"), "data"))

# 添加logs目录（如果存在）
if (project_root / "logs").exists():
    datas.append((str(project_root / "logs"), "logs"))

# 添加所有.md文件
md_files = glob.glob(str(project_root / "*.md"))
for md_file in md_files:
    datas.append((md_file, "."))

# 隐藏导入模块 - 针对Python 3.13优化
hiddenimports = [
    # PyQt5/PyQt6 核心模块
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'PyQt5.QtWebEngineWidgets',
    'PyQt5.QtWebEngineCore',
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'PyQt6.QtWebEngineWidgets',
    'PyQt6.QtWebEngineCore',

    # 网络和数据处理
    'requests',
    'requests.adapters',
    'requests.auth',
    'requests.cookies',
    'requests.exceptions',
    'urllib3',
    'urllib3.util',
    'urllib3.util.retry',
    'urllib3.poolmanager',

    # 数据处理
    'pandas',
    'pandas.io.excel',
    'pandas.io.common',
    'openpyxl',
    'openpyxl.workbook',
    'openpyxl.worksheet',
    'openpyxl.styles',

    # Python标准库
    'json',
    'pathlib',
    'datetime',
    'threading',
    'concurrent.futures',
    'logging',
    'logging.handlers',
    'urllib.parse',
    'subprocess',
    'time',
    're',
    'collections',
    'collections.abc',
    'functools',
    'itertools',
    'weakref',

    # 编码相关 - Python 3.13重要
    'encodings',
    'encodings.utf_8',
    'encodings.cp1252',
    'encodings.gbk',
    'locale',

    # 其他可能需要的模块
    'queue',
    'socket',
    'ssl',
    'hashlib',
    'base64',
    'uuid',
    'platform',
    'tempfile',
    'shutil',
    'zipfile'
]

# 排除的模块 - 减少打包大小
excludes = [
    'tkinter',
    'matplotlib',
    'numpy.distutils',
    'scipy',
    'IPython',
    'jupyter',
    'notebook',
    'qtconsole',
    'spyder',
    'pylsp',
    'pytest',
    'setuptools',
    'pip',
    'wheel',
    'distutils',
    'test',
    'tests',
    'unittest',
    'doctest'
]

# 分析配置 - 针对Python 3.13优化
a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={
        # PyQt5/PyQt6 钩子配置
        'PyQt5': {
            'qt_plugins': ['platforms', 'imageformats', 'iconengines']
        },
        'PyQt6': {
            'qt_plugins': ['platforms', 'imageformats', 'iconengines']
        }
    },
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
    # Python 3.13 特定配置
    module_collection_mode={
        'requests': 'py',
        'pandas': 'py',
        'openpyxl': 'py'
    }
)

# 去除重复文件和优化
pyz = PYZ(
    a.pure,
    a.zipped_data,
    cipher=None,
    # 压缩优化
    compress_level=6
)

# 创建Windows清单文件内容 - 防止中文乱码
manifest_template = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <assemblyIdentity
    version="*******"
    processorArchitecture="*"
    name="快手采集工具"
    type="win32"
  />
  <description>快手采集工具</description>
  <application xmlns="urn:schemas-microsoft-com:asm.v3">
    <windowsSettings>
      <activeCodePage xmlns="http://schemas.microsoft.com/SMI/2019/WindowsSettings">UTF-8</activeCodePage>
      <heapType xmlns="http://schemas.microsoft.com/SMI/2020/WindowsSettings">SegmentHeap</heapType>
    </windowsSettings>
  </application>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
    </application>
  </compatibility>
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v2">
    <security>
      <requestedPrivileges xmlns="urn:schemas-microsoft-com:asm.v3">
        <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
</assembly>'''

# 将清单内容写入临时文件
manifest_file = project_root / "app.manifest"
with open(manifest_file, 'w', encoding='utf-8') as f:
    f.write(manifest_template)

# EXE配置 - 针对Python 3.13和中文乱码优化
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='快手采集工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[
        # 排除可能导致问题的文件
        'vcruntime140.dll',
        'python3.dll',
        'python313.dll'
    ],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
    version_file=None,
    uac_admin=False,
    uac_uiaccess=False,
    # 防止中文乱码的关键配置
    manifest=str(manifest_file),
    # 额外的Windows特定配置
    hide_console='hide-early'
)

# 清理临时清单文件
try:
    os.remove(manifest_file)
except:
    pass
