#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手采集工具 - 启动脚本
设置正确的编码环境并启动主程序
"""

import os
import sys
from pathlib import Path

def setup_encoding():
    """设置编码环境"""
    # 设置环境变量防止中文乱码
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    
    # 对于Windows系统的额外设置
    if sys.platform.startswith('win'):
        os.environ['PYTHONLEGACYWINDOWSSTDIO'] = 'utf-8'
        
        # 设置控制台代码页为UTF-8
        try:
            import subprocess
            subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
        except:
            pass

def check_dependencies():
    """检查必要的依赖"""
    missing_deps = []
    
    # 检查PyQt
    pyqt_available = False
    try:
        import PyQt5
        pyqt_available = True
    except ImportError:
        try:
            import PyQt6
            pyqt_available = True
        except ImportError:
            missing_deps.append('PyQt5 或 PyQt6')
    
    # 检查其他依赖
    deps = ['requests', 'pandas', 'openpyxl']
    for dep in deps:
        try:
            __import__(dep)
        except ImportError:
            missing_deps.append(dep)
    
    if missing_deps:
        print("错误：缺少以下依赖包：")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\n请运行以下命令安装依赖：")
        print("  pip install -r requirements.txt")
        print("或运行：")
        print("  install_dependencies.bat")
        return False
    
    return True

def main():
    """主函数"""
    print("快手采集工具启动中...")
    
    # 设置编码
    setup_encoding()
    
    # 检查依赖
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    # 检查主程序文件
    main_file = Path("main.py")
    if not main_file.exists():
        print("错误：未找到主程序文件 main.py")
        input("按回车键退出...")
        return
    
    # 启动主程序
    try:
        print("正在启动主程序...")
        import main
        main.main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
